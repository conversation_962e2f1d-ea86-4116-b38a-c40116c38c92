NODE_ENV=staging
PORT=8080
# Cloud Run URL format: https://SERVICE_NAME-PROJECT_NUMBER.REGION.run.app
# This will be updated during deployment with actual Cloud Run URL
SERVER_ORIGIN=https://aida-service-staging-PROJECT_NUMBER.europe-west2.run.app
GCP_PROJECT_ID=aida---stg
GCP_LOCATION=europe-west2

# db
DB_HOST=/cloudsql/aida---stg:europe-west2:aida-db-stg
DB_USERNAME=postgres
DB_DATABASE=aida

GCS_BUCKET=aida-storages-stg

DEFAULT_TIMEZONE=Europe/London

# Recall
RECALL_API_KEY=a760403330e0b2ac369e79804b709f1d84c7dc1e
RECALL_API_BASE_URL_V1=https://eu-central-1.recall.ai/api/v1
RECALL_API_BASE_URL_V2=https://eu-central-1.recall.ai/api/v2
RECALL_SYSTEM_CALENDAR_EMAIL=<EMAIL>

GCP_PUBSUB_TOPIC_NAME=projects/aida---stg/topics/cloudlab-resouce
GCP_PUBSUB_SUBSCRIPTION_NAME=projects/aida---stg/subscriptions/cloudlab-resouce-sub
GCP_PUBSUB_TRANSCRIBER_TOPIC_NAME=projects/aida---stg/topics/staging-transcoder-notify
GCP_PUBSUB_TRANSCRIBER_SUBSCRIPTION_NAME=projects/aida---stg/subscriptions/staging-transcoder-notify-sub
AIDA_API_KEY=8IXtyR898g8OplVd9frruCzcf7sqXQINeH5GaUp7l28Knd03aFmpF3bXuhLTFcFX

#sendgrid
SENDGRID_FROM_EMAIL='<EMAIL>'
SENDGRID_FROM_NAME='Aida @ Beings'

# gemini
GEMINI_MODEL=gemini-2.0-flash

# aida client
AIDA_ENDPOINT=https://staging-aida.beings.com

JWT_SECRET=aIdAsTg-secret

# Posthog
POSTHOG_KEY=phc_jBecw1fNkgqvMbvjCK8rp11FIsKTSkTTGwxDRLSq54x
POSTHOG_HOST=https://us.i.posthog.com

WEB_APP_URL=https://staging-aida.beings.com
