---
description: 
globs: 
alwaysApply: true
---
# AIDA Backend - AI Agent Context (Master Rule)

## What You're Working On
**AIDA** is a collaborative AI platform for meeting transcription and resource management. You're helping with the **Node.js/TypeScript backend API**.

**Architecture**: `Routes → Controllers → Handlers → Services → Models`  
**Stack**: Node.js 20 + TypeScript + Express.js + PostgreSQL + Firebase Auth + Google Cloud

## Which Rules to Check

**🎯 ALWAYS**: `typescript.mdc` + `architecture.mdc`

**For specific tasks**:
- **API endpoints** → `express.mdc`
- **Database** → `database.mdc` 
- **Projects/collaboration** → `projects.mdc`
- **File uploads/media** → `resources.mdc`
- **Security/auth** → `security.mdc`
- **Deployment** → `cloud.mdc`

## Key Patterns You Must Know

### 🔐 Authentication Flow
```typescript
router.post('/endpoint',
  checkAuthMiddleware,    // Sets res.locals.user
  validateInput,          // Joi validation  
  businessHandler         // Actual logic
);
```

### 📦 Service Response Format
```typescript
interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  message?: string; 
  statusCode: number;
  meta?: { total?: number; page?: number; };
}
```

### 🗃️ Database Transaction Pattern
```typescript
const transaction = await db.transaction();
try {
  await Model.create(data, { transaction });
  await transaction.commit();
  return { success: true, data, statusCode: 201 };
} catch (error) {
  await transaction.rollback();
  throw error;
}
```

### 🛡️ Permission Check Pattern
```typescript
const hasAccess = await checkProjectPermission(
  projectId, 
  userId, 
  'member' // or 'admin', 'owner'
);
if (!hasAccess) {
  return res.status(403).json(createErrorResponse('Access denied'));
}
```

## Critical Patterns to Follow
1. **Layer Separation**: Routes → Controllers → Handlers → Services → Models
2. **File Naming**: `[action][Entity]Handler.ts`, `[entity].service.ts`, `[Entity].model.ts`
3. **Imports**: Use `@/` alias, external packages first
4. **Type Safety**: Explicit types, avoid `any`
5. **Auth**: Always check permissions with `checkProjectPermission()`
6. **Errors**: Try/catch with logging, return `ServiceResponse` format

---
**🎯 This rule provides the foundation. Always reference it first, then check specific rules based on what you're building!**
