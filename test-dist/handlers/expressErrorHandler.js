"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.express = void 0;
const express_1 = require("express");
exports.express = express_1.default;
const logger_1 = require("@/services/logger");
const errors_1 = require("@/utils/errors");
const expressErrorHandler = (err, req, res, next) => {
    if (!err) {
        err = new errors_1.Error404("The requested url was not found");
    }
    else if (typeof err.json !== "function") {
        logger_1.log.stack(err);
        err = new errors_1.IntentionalError("An error occurred", err);
    }
    res.status(err.code).json(err.json());
};
exports.default = expressErrorHandler;
