"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const cors_1 = require("cors");
const express_1 = require("express");
require("express-async-errors");
const api_1 = require("@/api");
const schemas_1 = require("@/schemas");
const logger_1 = require("@/services/logger");
const posthog_1 = require("@/services/posthog");
const expressErrorHandler_1 = require("./handlers/expressErrorHandler");
const firebase_1 = require("./services/firebase");
const pubsub_1 = require("./services/pubsub");
const app = (0, express_1.default)();
const PORT = parseInt(process.env.PORT || "8080", 10);
app.use((0, cors_1.default)());
app.use(express_1.default.json({ limit: "50mb" }));
app.use(express_1.default.urlencoded({ limit: "50mb", extended: true }));
app.use("/api", api_1.default);
app.use(expressErrorHandler_1.default);
const main = async () => {
    console.info("🚀 Starting Aida Service...");
    console.info(`Environment: ${process.env.NODE_ENV || "development"}`);
    console.info(`Build Mode: ${process.env.BUILD_MODE || "not set"}`);
    console.info(`Port: ${PORT}`);
    try {
        console.info("🔧 Initializing services...");
        await Promise.all([
            (0, firebase_1.initializeFirebase)(), // initialize firebase
            schemas_1.db.authenticate(), // check db authentication
        ]);
        console.info("✅ Services initialized successfully");
        // Sync DB
        console.info("🗄️ Syncing database...");
        // TODO: Remove "alter" option later; keep it here for now for faster development due to many changes with schema
        await schemas_1.db.sync({
            alter: true,
        });
        console.info("✅ Database synced successfully");
        // Subscribe to PubSub messages
        console.info("📡 Initializing PubSub...");
        await (0, pubsub_1.initPubSub)();
        console.info("✅ PubSub initialized successfully");
    }
    catch (error) {
        console.error("❌ Failed to initialize services:", error);
        throw error;
    }
    // Initialize scheduler
    // const schedulerService = new SchedulerService();
    // schedulerService.start();
    // Start server - bind to 0.0.0.0 for Cloud Run
    const server = app.listen(PORT, "0.0.0.0", () => {
        console.info(`Successfully started the server on PORT: ${PORT}`);
        console.info(`Server listening on 0.0.0.0:${PORT}`);
        console.info(`Environment: ${process.env.NODE_ENV || "development"}`);
        console.info(`Build Mode: ${process.env.BUILD_MODE || "not set"}`);
        // Capture server start event
        posthog_1.default.capture({
            distinctId: "server",
            event: "server_started",
            properties: {
                port: PORT,
                environment: process.env.NODE_ENV || "development",
            },
        });
    });
    // Graceful shutdown handler for Cloud Run
    const gracefulShutdown = async (signal) => {
        console.info(`Received ${signal}. Starting graceful shutdown...`);
        // Stop accepting new requests
        server.close(async () => {
            console.info("HTTP server closed");
            try {
                // Close database connections
                await schemas_1.db.close();
                console.info("Database connections closed");
                // Capture shutdown event
                posthog_1.default.capture({
                    distinctId: "server",
                    event: "server_shutdown",
                    properties: {
                        signal,
                        environment: process.env.NODE_ENV || "development",
                    },
                });
                console.info("Graceful shutdown completed");
                process.exit(0);
            }
            catch (error) {
                console.error("Error during graceful shutdown:", error);
                process.exit(1);
            }
        });
        // Force shutdown after 30 seconds
        setTimeout(() => {
            console.error("Forced shutdown after timeout");
            process.exit(1);
        }, 30000);
    };
    // Handle shutdown signals
    process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
    process.on("SIGINT", () => gracefulShutdown("SIGINT"));
};
main().catch((err) => {
    logger_1.log.stack(err, "fatal");
    // Capture fatal error event
    posthog_1.default.capture({
        distinctId: "server",
        event: "fatal_error",
        properties: {
            error: err.message,
            stack: err.stack,
        },
    });
});
