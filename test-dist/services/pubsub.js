"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initPubSub = exports.transcoderPubsubService = exports.pubsubService = void 0;
const pubsub_1 = require("@google-cloud/pubsub");
const logger_1 = require("@/services/logger");
const config_1 = require("@/config");
const subcribers_1 = require("@/handlers/subcribers");
class PubSubService {
    pubsub = null;
    topicName;
    subscriptionName;
    constructor(topicName, subscriptionName) {
        this.topicName = topicName;
        this.subscriptionName = subscriptionName;
    }
    async getPubSub() {
        if (!this.pubsub) {
            const serviceAccount = await (0, config_1.getGcsServiceAccount)();
            this.pubsub = new pubsub_1.PubSub({ credentials: serviceAccount });
        }
        return this.pubsub;
    }
    async publishMessage(data, attributes) {
        const pubsub = await this.getPubSub();
        const messageBuffer = Buffer.from(JSON.stringify(data));
        const messageId = await pubsub
            .topic(this.topicName)
            .publish(messageBuffer, attributes);
        logger_1.log.info(`Message ${messageId} published to topic ${this.topicName}`);
        return messageId;
    }
    async startListening(handler) {
        const pubsub = await this.getPubSub();
        const subscription = pubsub.subscription(this.subscriptionName);
        subscription.on("message", handler);
        subscription.on("error", (error) => {
            logger_1.log.error("Subscription error:", error);
        });
        logger_1.log.info(`Started listening to subscription ${this.subscriptionName}`);
    }
    async createTopic() {
        try {
            const [topic] = await this.pubsub.createTopic(this.topicName);
            logger_1.log.info(`Topic ${topic.name} created successfully`);
        }
        catch (error) {
            logger_1.log.error("Error creating topic:", error);
            throw error;
        }
    }
    async createSubscription() {
        try {
            const [subscription] = await this.pubsub
                .topic(this.topicName)
                .createSubscription(this.subscriptionName);
            logger_1.log.info(`Subscription ${subscription.name} created successfully`);
        }
        catch (error) {
            logger_1.log.error("Error creating subscription:", error);
            throw error;
        }
    }
}
exports.pubsubService = new PubSubService(config_1.gcpPubsubTopicName, config_1.gcpPubsubSubscriptionName);
exports.transcoderPubsubService = new PubSubService(config_1.gcpPubsubTranscoderTopicName, config_1.gcpPubsubTranscoderSubscriptionName);
const initPubSub = async () => {
    exports.pubsubService
        .startListening(async (message) => {
        try {
            const data = JSON.parse(message.data.toString());
            const messageType = message.attributes?.type;
            if (messageType && messageType in subcribers_1.subcribersHandler) {
                await subcribers_1.subcribersHandler[messageType](data, message.attributes);
            }
            else {
                logger_1.log.warn(`No handler found for message type: ${messageType}`);
            }
            message.ack();
        }
        catch (error) {
            logger_1.log.error("Error processing message:", error);
            message.nack();
        }
    })
        .catch((err) => logger_1.log.error("Error setting up PubSub subscription:", err));
    exports.transcoderPubsubService
        .startListening(async (message) => {
        try {
            const data = JSON.parse(message.data.toString());
            const messageType = message.attributes?.type;
            const handler = subcribers_1.subcribersHandler[config_1.AidaPubsubTopic.TRANSCODING_SYNC];
            if (handler) {
                await handler(data, message.attributes);
            }
            else {
                logger_1.log.warn(`No handler found for message type: ${messageType}`);
            }
            message.ack();
        }
        catch (error) {
            logger_1.log.error("Error processing message:", error);
            message.nack();
        }
    })
        .catch((err) => logger_1.log.error("Error setting up PubSub subscription:", err));
};
exports.initPubSub = initPubSub;
