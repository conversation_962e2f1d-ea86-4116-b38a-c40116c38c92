"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchedulerService = void 0;
const node_schedule_1 = require("node-schedule");
const logger_1 = require("@/services/logger");
const config_1 = require("@/config");
const retention_1 = require("@/services/retention");
class SchedulerService {
    retentionService;
    constructor() {
        this.retentionService = new retention_1.RetentionService();
    }
    start() {
        // Schedule retention cleanup
        node_schedule_1.default.scheduleJob(config_1.DATA_RETENTION_CLEANUP_SCHEDULE, async () => {
            logger_1.log.info('Running scheduled retention cleanup');
            try {
                await this.retentionService.run();
            }
            catch (error) {
                logger_1.log.error('Error in scheduled retention cleanup:', error);
            }
        });
        // this.retentionService.run();
        logger_1.log.info('Scheduler service started');
    }
}
exports.SchedulerService = SchedulerService;
