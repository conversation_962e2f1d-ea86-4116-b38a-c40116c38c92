"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getFirebaseApp = exports.syncUsersHandler = exports.getFirebaseUserDetails = exports.getFirebaseListOfUsersByEmails = exports.getFirebaseListOfUsersByIds = exports.getFirebaseUserById = exports.getFirebaseUserByEmail = exports.getFirebaseDb = exports.getFirebaseAuth = exports.initializeFirebase = exports.app = exports.FirebaseUser = void 0;
const app_1 = require("firebase-admin/app");
const auth_1 = require("firebase-admin/auth");
const firestore_1 = require("firebase-admin/firestore");
const config_1 = require("@/config");
const user_1 = require("@/types/user");
Object.defineProperty(exports, "FirebaseUser", { enumerable: true, get: function () { return user_1.FirebaseUser; } });
let auth;
let db;
const initializeFirebase = async () => {
    // Check if Firebase is already initialized
    const existingApps = (0, app_1.getApps)();
    if (existingApps.length > 0) {
        exports.app = existingApps[0];
        auth = (0, auth_1.getAuth)(exports.app);
        db = (0, firestore_1.getFirestore)(exports.app);
        return { app: exports.app, auth, db };
    }
    if (!exports.app) {
        const serviceAccount = await (0, config_1.getGcsServiceAccount)();
        exports.app = (0, app_1.initializeApp)({
            credential: (0, app_1.cert)(serviceAccount),
        });
        auth = (0, auth_1.getAuth)(exports.app);
        db = (0, firestore_1.getFirestore)(exports.app);
    }
    return { app: exports.app, auth, db };
};
exports.initializeFirebase = initializeFirebase;
const getFirebaseAuth = async () => {
    const { auth } = await (0, exports.initializeFirebase)();
    return auth;
};
exports.getFirebaseAuth = getFirebaseAuth;
const getFirebaseDb = async () => {
    const { db } = await (0, exports.initializeFirebase)();
    return db;
};
exports.getFirebaseDb = getFirebaseDb;
const getFirebaseUserByEmail = async (email) => {
    const auth = await (0, exports.getFirebaseAuth)();
    const user = await auth.getUserByEmail(email).catch(() => null);
    return user;
};
exports.getFirebaseUserByEmail = getFirebaseUserByEmail;
const getFirebaseUserById = async (uid) => {
    const auth = await (0, exports.getFirebaseAuth)();
    const user = await auth.getUser(uid);
    return user;
};
exports.getFirebaseUserById = getFirebaseUserById;
const getFirebaseListOfUsersByIds = async (uids) => {
    const auth = await (0, exports.getFirebaseAuth)();
    const getUsersResult = await auth.getUsers(uids.map((uid) => ({ uid })));
    return getUsersResult.users;
};
exports.getFirebaseListOfUsersByIds = getFirebaseListOfUsersByIds;
const getFirebaseListOfUsersByEmails = async (emails) => {
    const auth = await (0, exports.getFirebaseAuth)();
    const getUsersResult = await auth.getUsers(emails.map((email) => ({ email })));
    return getUsersResult.users;
};
exports.getFirebaseListOfUsersByEmails = getFirebaseListOfUsersByEmails;
const getFirebaseUserDetails = async (uid) => {
    const db = await (0, exports.getFirebaseDb)();
    const doc = await db.collection("users").doc(uid).get();
    if (!doc.exists) {
        return null;
    }
    return doc.data();
};
exports.getFirebaseUserDetails = getFirebaseUserDetails;
const syncUsersHandler = async () => {
    const auth = await (0, exports.getFirebaseAuth)();
    const db = await (0, exports.getFirebaseDb)();
    const result = await auth.listUsers(1000);
    for (const user of result.users) {
        const providerIds = user.providerData.map((p) => p.providerId);
        // Filter users with Google or Microsoft login
        const isGoogleOrMicrosoft = providerIds.includes("google.com") ||
            providerIds.includes("microsoft.com");
        if (isGoogleOrMicrosoft) {
            const userDoc = db.collection("users").doc(user.uid);
            const userData = {
                uid: user.uid,
                email: user.email || null,
                displayName: user.displayName || null,
                preferences: {
                    timezone: config_1.defaultTimezone,
                },
            };
            try {
                await userDoc.set(userData, { merge: true });
                console.log(`✔️ Synced: ${user.email}`);
            }
            catch (error) {
                console.error(`❌ Failed to sync ${user.email}:`, error.message);
            }
        }
    }
};
exports.syncUsersHandler = syncUsersHandler;
// Export a function to get the initialized app
const getFirebaseApp = async () => {
    const { app } = await (0, exports.initializeFirebase)();
    return app;
};
exports.getFirebaseApp = getFirebaseApp;
