import { models } from "@/schemas";
import { getResourceData } from "@/schemas/resource/utils";
import { ProjectData } from "@/types/project";
import ProjectModel, { Project } from "@/schemas/project/Project.model";
import { ProjectFolder } from "@/schemas/project/ProjectFolder.model";
import { Resource } from "@/schemas/resource/Resource.model";
import {
  ProjectMember,
  ProjectMemberRole,
} from "@/schemas/project/ProjectMember.model";
import { Op } from "sequelize";
import { createLogger } from "@/services/logger";

const logger = createLogger("ProjectService");

type ProjectDetails = Project & {
  resources: Resource[];
  folders: ProjectFolder[];
  members: ProjectMember[];
  userPermissions?: {
    canView: boolean;
    canEdit: boolean;
    canComment: boolean;
  };
};

export const getProjectById = async (projectId: string) => {
  const data = await ProjectModel.xFind1({
    id: projectId,
  });

  if (!data) {
    return {
      success: false,
      message: "Project not found",
      statusCode: 404,
    };
  }

  return {
    success: true,
    message: "Project found",
    statusCode: 200,
    data,
  };
};

export const verifyOwnedProject = async (userId: string, projectId: string) => {
  const data = await ProjectModel.xFind1({
    createdById: userId,
    id: projectId,
  });

  const statusCode = data ? 200 : 404;
  const message = data ? "Project found" : "Project not found";

  return {
    success: statusCode === 200,
    message,
    statusCode,
    data,
  };
};

export const getProjectDetails = async (
  projectId: string,
  userId: string,
  includeAllResources: boolean
) => {
  const project = await ProjectModel.xFind1({
    id: projectId,
  });

  if (!project) {
    return {
      success: false,
      message: "Project not found",
      statusCode: 404,
    };
  }

  const projectDetailsRaw = await models.Project.findOne({
    where: {
      id: projectId,
    },
    include: [
      {
        model: models.ProjectFolder,
        as: "folders",
      },
      {
        model: models.Resource,
        as: "resources",
        attributes: {
          exclude: ["ffprobe"],
        },
      },
      {
        model: models.ProjectMember,
        as: "members",
        attributes: ["id", "role", "userId"],
        where: {
          userId: userId,
        },
        required: false,
      },
    ],
  });

  const projectDetails = projectDetailsRaw.toJSON<ProjectDetails>();
  const userPermissions = getProjectUserPermissions(projectDetails, userId);

  const formattedResources = await Promise.all(
    projectDetails.resources
      .filter((resource) => (includeAllResources ? true : !resource.folderId))
      .map(async (resource) => {
        let resourceData = await getResourceData(resource);
        return {
          ...resourceData,
          userPermissions,
        };
      })
  );

  const output = {
    ...projectDetails,
    resources: formattedResources,
    totalFilesCount: projectDetails.resources.length,
    totalFilesSize: formattedResources.reduce(
      (acc, curr) => acc + (curr.fileSize ?? 0),
      0
    ),
  } as ProjectData;

  return {
    success: true,
    message: "Project details retrieved successfully",
    statusCode: 200,
    data: output,
  };
};

export const getProjects = async (
  userId: string,
  accessibleProjects: string[]
) => {
  try {
    const projects = await models.Project.findAll({
      where: {
        [Op.or]: [
          {
            createdById: userId,
          },
          {
            id: {
              [Op.in]: accessibleProjects,
            },
          },
        ],
      },
      include: [
        {
          model: models.ProjectMember,
          attributes: ["id", "role", "userId"],
          as: "members",
          where: {
            userId: userId,
          },
          required: false,
        },
        {
          model: models.Resource,
          attributes: ["id", "fileSize"],
          as: "resources",
        },
      ],
      order: [["createdAt", "DESC"]],
    });

    const output = projects.map((p) => {
      const project = p.toJSON<ProjectDetails>();
      const userPermissions = getProjectUserPermissions(project, userId);
      const totalFilesSize = project.resources.reduce(
        (acc, curr) => acc + (curr.fileSize ?? 0),
        0
      );
      const totalFilesCount = project.resources.length;

      return { ...project, totalFilesSize, totalFilesCount, userPermissions };
    });

    return {
      success: true,
      message: "Project details retrieved successfully",
      statusCode: 200,
      data: output,
    };
  } catch (error) {
    logger.error(`Failed to get projects: ${error.message}`);

    return {
      success: false,
      message: "Failed to get projects",
      statusCode: 500,
      data: [],
    };
  }
};

export const getProjectDefault = async (userId: string) => {
  try {
    const project = await models.Project.findOne({
      where: {
        isDefault: true,
        createdById: userId,
      },
    });

    if (!project) {
      return {
        success: false,
        message: "Project not found",
        statusCode: 404,
        data: null,
      };
    }
    return {
      success: true,
      message: "Project found",
      statusCode: 200,
      data: project.toJSON<Project>(),
    };
  } catch (error) {
    logger.error(`Failed to get project default: ${error.message}`);

    return {
      success: false,
      message: "Failed to get project default",
      statusCode: 500,
      data: null,
    };
  }
};

export const createProject = async (
  userId: string,
  name: string,
  description: string,
  isDefault?: boolean
) => {
  try {
    const project = await models.Project.xCreate({
      name,
      description,
      createdById: userId,
      isDefault: isDefault || false,
    });

    return {
      success: true,
      message: "Project created successfully",
      statusCode: 200,
      data: project,
    };
  } catch (error) {
    logger.error(`Failed to create project: ${error.message}`);

    return {
      success: false,
      message: "Failed to create project",
      statusCode: 500,
      data: null,
    };
  }
};

export const updateProject = async (
  projectId: string,
  userId: string,
  payload: Partial<Project>
) => {
  try {
    // First verify the project exists and user has permission
    const project = await ProjectModel.xFind1({
      id: projectId,
      createdById: userId,
    });

    if (!project) {
      return {
        success: false,
        message: "Project not found or you don't have permission to update it",
        statusCode: 404,
        data: null,
      };
    }

    // Update the project
    const updatedProject = await models.Project.update(payload, {
      where: {
        id: projectId,
        createdById: userId,
      },
      returning: true,
    });

    if (!updatedProject[0]) {
      return {
        success: false,
        message: "Failed to update project",
        statusCode: 500,
        data: null,
      };
    }

    return {
      success: true,
      message: "Project updated successfully",
      statusCode: 200,
      data: updatedProject[1][0],
    };
  } catch (error) {
    logger.error(`Failed to update project: ${error.message}`);

    return {
      success: false,
      message: "Failed to update project",
      statusCode: 500,
      data: null,
    };
  }
};

/**************************************************
 *             PRIVATE FUNCTIONS
 *
 ***************************************************/

function getProjectUserPermissions(project: ProjectDetails, userId: string) {
  const isProjectOwner = project.createdById === userId;
  const projectMember = project.members.find(
    (member) => member.userId === userId
  );
  const memberRole = projectMember?.role;
  
  const canEdit = isProjectOwner || memberRole === ProjectMemberRole.EDITOR;
  const canComment = canEdit || memberRole === ProjectMemberRole.COMMENTER;
  
  return {
    canView: true,
    canEdit,
    canComment,
  };
}
