import sendgrid from '@sendgrid/mail'
import { sendgridApiKey, sendgridFromName, sendgridFromEmail, webAppUrl } from '@/config'
import { confirmedMeetingTemplate, MeetingType } from './template/confirmedMeeting'
import { generateSummaryTemplate, SummarySection } from './template/summary'
import { projectInvitationTemplate } from './template/projectInvitation'
import { projectAccessRequestTemplate } from './template/projectAccessRequest'
import { projectAccessRequestRejectedTemplate } from './template/projectAccessRequestRejected'
import { createLogger } from '@/services/logger'
import { SendgridTemplates } from './template'
import { projectAccessRequestApprovedTemplate } from './template/projectAccessRequestApproved'
import { projectRoleChangedTemplate } from './template/projectRoleChanged'
import { getFirebaseUserByEmail } from '../firebase'
import { confirmAidaFirstJoinTemplate } from './template/confirmAidaFirstJoin'

if (sendgridApiKey) { sendgrid.setApiKey(sendgridApiKey) }

const logger = createLogger('EmailService')

export interface SendMailParams {
    to: string | string[]
    cc?: string | string[]
    subject: string
    text?: string
    html?: string
    attachments?: Array<Partial<{
        content: string
        filename: string
        type: string
        disposition: 'attachment' | 'inline'
    }>>
    substitutions?: Record<string, string>
    dynamicTemplateData?: Record<string, any>
}

export type SendSendMailParams = {
    html?: string
    templateId?: string
}

const sendEmail = async (options: SendMailParams, sendOptions: SendSendMailParams): Promise<boolean> => {
    try {
        logger.info(`Sending email with options: ${JSON.stringify({
            to: options.to,
            cc: options.cc,
            subject: options.subject,
            text: options.text,
            hasHTML: options.html !== undefined,
            hasAttachments: options.attachments !== undefined,
        })}`)

        const msg = {
            personalizations: [{
                to: Array.isArray(options.to) ? options.to.map(email => ({ email })) : [{ email: options.to }],
                cc: Array.isArray(options.cc) ? options.cc.map(email => ({ email })) : options.cc ? [{ email: options.cc }] : undefined,
            }],
            from: {
                name: sendgridFromName,
                email: sendgridFromEmail,
            },
            subject: options.subject,
            text: options.text,
            ...sendOptions,
            ...(sendOptions.templateId ? { dynamicTemplateData: options.dynamicTemplateData } : {substitutions: options.substitutions, substitutionWrappers: ['{{', '}}']}),
        }

        await sendgrid.send(msg)
        logger.info('Email sent successfully', { to: options.to, subject: options.subject, msg })
        return true
    } catch (error) {
        logger.error('Error sending email:', error)
        return false
    }
}

export const sendConfirmAidaFirstJoinEmail = async (params: SendMailParams): Promise<boolean> => {
    return sendEmail({
        to: params.to,
        subject: params.subject,
        cc: params.cc,
        attachments: params.attachments,
        dynamicTemplateData: params.substitutions,
    }, {
        html: confirmAidaFirstJoinTemplate(params.substitutions.username, params.substitutions.magicLink),
    })
}

export const sendVerificationEmail = async (params: SendMailParams): Promise<boolean> => {
    return sendEmail({
        to: params.to,
        subject: params.subject,
        cc: params.cc,
        attachments: params.attachments,
        dynamicTemplateData: params.substitutions,
    }, {
        templateId: SendgridTemplates.AIDA_GUEST_VERIFICATION,
    })
}

export const sendConfirmedMeetingEmail = async (params: SendMailParams, type: MeetingType): Promise<boolean> => {
    return sendEmail({
        to: params.to,
        subject: params.subject,
        cc: params.cc,
        attachments: params.attachments,
        dynamicTemplateData: params.substitutions,
    }, {
        html: confirmedMeetingTemplate(type, params.substitutions),
    })
}

export const sendSessionDeletedEmail = async (params: SendMailParams): Promise<boolean> => {
    return sendEmail({
        to: params.to,
        subject: params.subject,
        cc: params.cc,
        attachments: params.attachments,
        dynamicTemplateData: params.substitutions,
    }, {
        templateId: SendgridTemplates.AIDA_SESSION_DELETED,
    })
}

export const sendSessionUpdatedEmail = async (params: SendMailParams): Promise<boolean> => {
    return sendEmail({
        to: params.to,
        subject: params.subject,
        cc: params.cc,
        attachments: params.attachments,
        dynamicTemplateData: params.substitutions,
    }, {
        templateId: SendgridTemplates.AIDA_SESSION_UPDATED,
    })
}


export const sendSummary = async (params: SendMailParams, sections: SummarySection[]): Promise<boolean> => {
    return sendEmail({
        to: params.to,
        subject: params.subject,
        cc: params.cc,
        attachments: params.attachments,
        substitutions: params.substitutions,
    }, {
        html: generateSummaryTemplate(sections),
    })
}

interface SendInvitationEmailPayload {
    to: string
    projectName: string
    code: string
    projectId: string
    message?: string
    invitedByUserEmail?: string
    projectOwnerName?: string
    recipientName?: string
}

export const sendProjectInvitationEmail = async (payload: SendInvitationEmailPayload): Promise<boolean> => {
    const { to, projectName, code, projectId, message, invitedByUserEmail } = payload
    logger.info('Sending invitation email', { to, projectName, code, message })

    const invitationLink = `${webAppUrl}/accept-project-invitation?code=${code}&projectId=${projectId}`
    const subject = `Project invite: ${projectName}`

    const [getSenderUserResult, getRecipientUserResult] = await Promise.all([
        getFirebaseUserByEmail(invitedByUserEmail),
        getFirebaseUserByEmail(to)
    ])

    const projectOwnerName = getSenderUserResult?.displayName
    const recipientName = getRecipientUserResult?.displayName

    return sendEmail({
        to,
        subject,
        // cc: invitedByUserEmail ? [invitedByUserEmail] : undefined, // TODO: Uncomment this when we have a way to send cc emails
    }, {
        html: projectInvitationTemplate({ projectName, invitationLink, projectOwnerName, recipientName, projectOwnerEmail: invitedByUserEmail}),
    })
}

interface SendProjectAccessRequestEmailPayload {
    requesterEmail: string
    approverEmail: string
    projectName: string
    projectId: string
    requestId: string
    requesterName: string
    approverName: string
}

export const sendProjectAccessRequestEmail = async (payload: SendProjectAccessRequestEmailPayload): Promise<boolean> => {
    const { approverEmail, projectName, projectId, requestId, requesterEmail } = payload
    logger.info(`Sending project access request email to ${approverEmail} for project ${projectName}`)

    const [getRequesterUserResult, getApproverUserResult] = await Promise.all([
        getFirebaseUserByEmail(requesterEmail),
        getFirebaseUserByEmail(approverEmail)
    ])

    const requesterName = getRequesterUserResult?.displayName
    const approverName = getApproverUserResult?.displayName
    return sendEmail({
        to: approverEmail,
        subject: `Access request to project: ${projectName} on AIDA`,
    }, {
        html: projectAccessRequestTemplate({ requesterEmail, projectName, projectId, requestId, requesterName, approverName }),
    })
}

interface SendProjectAccessRequestRejectedEmailPayload {
    projectName: string
    requesterEmail: string
    rejectedReason: string
    userName?: string
}

export const sendProjectAccessRequestRejectedEmail = async (payload: SendProjectAccessRequestRejectedEmailPayload): Promise<boolean> => {
    const { projectName, requesterEmail, rejectedReason, userName } = payload
    return sendEmail({
        to: requesterEmail,
        subject: `Access denied for "${projectName}"`,
    }, {
        html: projectAccessRequestRejectedTemplate({ requesterEmail, projectName, rejectedReason, userName }),
    })
}

interface SendProjectAccessRequestApprovedEmailPayload {
    projectName: string
    requesterEmail: string
    projectId: string
    userName?: string
}

export const sendProjectAccessRequestApprovedEmail = async (payload: SendProjectAccessRequestApprovedEmailPayload): Promise<boolean> => {
    const { projectName, requesterEmail, projectId, userName } = payload
    return sendEmail({
        to: requesterEmail,
        subject: `Access approved for "${projectName}"`,
    }, {
        html: projectAccessRequestApprovedTemplate({ projectName, projectId, userName }),
    })
}


export const sendMeetingDeletedEmail = async (params: SendMailParams): Promise<boolean> => {
    return sendEmail({
        to: params.to,
        subject: params.subject,
        dynamicTemplateData: params.substitutions,
    }, {
        templateId: SendgridTemplates.MEETING_DELETED,
    })
}

export const sendRetentionReminderEmail = async (params: SendMailParams & {
  substitutions: {
    meetingTitle: string;
    deletionDate: string;
    daysRemaining: string;
    extendUrl: string;
  }
}) => {
  return sendEmail({
    to: params.to,
    subject: params.subject,
    cc: params.cc,
    attachments: params.attachments,
    dynamicTemplateData: params.substitutions,
  }, {
    templateId: SendgridTemplates.RETENTION_REMINDER,
  })
}

interface SendProjectRoleChangedEmailPayload {
    to: string;
    projectName: string;
    projectId: string;
    newRole: string;
    userName?: string;
    changedByUserName?: string;
}

export const sendProjectRoleChangedEmail = async (payload: SendProjectRoleChangedEmailPayload): Promise<boolean> => {
    const { to, projectName, projectId, newRole, userName, changedByUserName } = payload;
    const subject = `Role updated in "${projectName}"`;

    return sendEmail({
        to,
        subject,
    }, {
        html: projectRoleChangedTemplate({ 
            projectName, 
            projectId, 
            userName, 
            newRole: newRole.charAt(0).toUpperCase() + newRole.slice(1).toLowerCase(), 
            changedByUserName 
        }),
    });
};

interface SendInvitationCanceledEmailPayload {
    to: string;
    projectName: string;
    projectId: string;
    canceledByUserEmail?: string;
    canceledByUserName?: string;
}

export const sendProjectInvitationCanceledEmail = async (payload: SendInvitationCanceledEmailPayload): Promise<boolean> => {
    const { to, projectName, canceledByUserEmail, canceledByUserName } = payload;
    logger.info('Sending invitation canceled email', { to, projectName });

    const subject = `Invitation to project "${projectName}" has been canceled`;
    const canceledByName = canceledByUserName || 'Project owner';

    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
</head>
<body>
<div>
    Hi there,<br /><br />

    We wanted to let you know that your invitation to collaborate on the project "${projectName}" has been canceled by ${canceledByName}.<br /><br />

    If you have any questions about this cancellation, please reach out to ${canceledByUserEmail || 'the project owner'}.<br /><br />

    Thank you.<br /><br />

    —<br />
    Aida<br />
    <EMAIL><br /><br />

    Beings is GDPR, ISO 27001, SOC 2 Type II, and HIPAA compliant.
</div>
</body>
</html>
    `;

    return sendEmail({
        to,
        subject,
        // cc: canceledByUserEmail ? [canceledByUserEmail] : undefined, // TODO: Uncomment this when we have a way to send cc emails
    }, {
        html,
    });
};
