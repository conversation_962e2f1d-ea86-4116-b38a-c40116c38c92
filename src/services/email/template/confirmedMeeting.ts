import { aidaFooter } from "./aidaFooter";

export enum MeetingType {
    Existed = "existed",
    Confirmed = "confirmed",
}

export const confirmedMeetingTemplate = (type: MeetingType, substitutions: Record<string, string>): string => {
    return `<table>
        <tr>
            <td>
                Hi ${substitutions.username},
            </td>
        </tr>
        ${getContentByType(type, substitutions)}
        ${aidaFooter}
    </table>`;
};

const getContentByType = (type: MeetingType, substitutions: Record<string, string>) => {
    if (type === MeetingType.Confirmed) {
        return `
           <tr>
                <td>
                    Your email is confirmed. <PERSON><PERSON> will be scheduled for your meeting.
                </td>
            </tr>
      `;
    }

    return `
    <tr>
        <td>
           Thanks for inviting me to your meeting:
        </td>
    </tr>
    <tr>
        <td>
            "${substitutions.title}" on ${substitutions.dateTime}
        </td>
    </tr>
    `;
};