import { simpleFooter } from "./simpleFooter";

interface ProjectAccessRequestRejectedTemplateParams {
    requesterEmail: string
    projectName: string
    rejectedReason: string
    userName?: string
}

export const projectAccessRequestRejectedTemplate = ({ requesterEmail, projectName, rejectedReason, userName }: ProjectAccessRequestRejectedTemplateParams): string => `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
</head>
<body>
<div>
    Hi ${userName || requesterEmail},<br /><br />

    Your request to join the project was denied by the owner.<br /><br />

    If this was a mistake, please contact the project owner.<br /><br />

    Thank you.<br /><br />

    ${simpleFooter}
</div>
</body>
</html>
` 