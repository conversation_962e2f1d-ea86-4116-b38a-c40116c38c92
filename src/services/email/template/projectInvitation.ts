
import { simpleFooter } from "./simpleFooter";

interface ProjectInvitationTemplateParams {
    projectName: string
    invitationLink: string
    message?: string
    projectOwnerName?: string
    recipientName?: string
    projectOwnerEmail?: string
}

export const projectInvitationTemplate = ({ 
    projectName, 
    invitationLink, 
    projectOwnerName = "Project owner",
    recipientName = "there",
    projectOwnerEmail
}: ProjectInvitationTemplateParams): string => `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
</head>
<body>
<div>
    Hi ${recipientName},<br /><br />

   ${projectOwnerName} (${projectOwnerEmail}) has invited you to join their project.<br /><br />

    Click to join: <a href="${invitationLink}">${projectName}</a><br /><br />

    Thank you.<br /><br />

    ${simpleFooter}
</div>
</body>
</html>
` 