import { webAppUrl } from '@/config'
import { simpleFooter } from "./simpleFooter";

interface ProjectRoleChangedTemplateParams {
    projectName: string
    projectId: string
    userName?: string
    newRole: string
    changedByUserName?: string
}

export const projectRoleChangedTemplate = ({ projectName, projectId, userName, newRole, changedByUserName }: ProjectRoleChangedTemplateParams): string => `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
</head>
<body>
<div>
    Hi ${userName || 'there'},<br /><br />

    Your role in ${projectName} has been changed to ${newRole} by ${changedByUserName}

    Access the project here: <a href="${webAppUrl}/project/${projectId}">${projectName}</a><br /><br />

    ${simpleFooter}
</div>
</body>
</html>
` 
