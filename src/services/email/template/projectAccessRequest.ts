import { webAppUrl } from '@/config'
import { simpleFooter } from "./simpleFooter";

interface ProjectAccessRequestTemplateParams {
    requesterEmail: string
    projectName: string
    projectId: string
    requestId: string
    requesterName: string
    approverName: string
}

export const projectAccessRequestTemplate = ({ requesterEmail, projectName, projectId, requestId, requesterName, approverName }: ProjectAccessRequestTemplateParams): string => `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
</head>
<body>
<div>
    Hi ${approverName},<br /><br />

    ${requesterName || requesterEmail} has requested access to your project: <a href="${webAppUrl}/project/${projectId}">${projectName}</a><br /><br />

    Click to view: <a href="${webAppUrl}/project/${projectId}#sharing">${projectName}</a><br /><br />

    Thank you.<br /><br />

    ${simpleFooter}
</div>
</body>
</html>
` 