import { models } from "@/schemas";
import type { Monologue, RevAi<PERSON><PERSON>Job } from "revai-node-sdk";
import { revAiClient } from "../revAi";
import { TranscriptStatus } from "@/schemas/resource/ResourceInInsightEngine.model";
import { upsertUserStatistics } from "@/schemas/statistics/utils";
import { submitExtractionJob } from ".";
import { log } from "../logger";
import { SessionStatus } from "@/schemas/session/Session.model";
import {
  generateSummaryAndStore,
  sendSummaryEmail,
} from "./generateSummary";
import { getFirebaseAuth, getFirebaseUserDetails } from "../firebase";
import { addDays } from "date-fns";
import { DATA_RETENTION_DAYS, defaultTimezone } from "@/config";
import { ResourceType } from "@/schemas/resource/Resource.model";
import { ResourceLiveTranscript } from "@/types/resources";

export const handleTranscriptSuccess = async (job: RevAi<PERSON>piJob) => {
  try {
    const resourceInIE = await models.ResourceInInsightEngine.xFind1By(
      "revAiJobId",
      job.id
    );
    if (!resourceInIE) {
      return;
    }
    const resource = await models.Resource.xFind1By(
      "id",
      resourceInIE.resourceId
    );
    const data = await revAiClient.getTranscriptObject(job.id);

    if (!data) {
      return;
    }

    // Submit topic extraction job
    // TODO: Enable this later after migrate RevAI to US region
    // log.info("[handleTranscriptSuccess] Submitting topic extraction job");
    // const extractionJob = await submitExtractionJob(data);

    // await models.ResourceInInsightEngine.xUpdateById(resourceInIE.id, {
    //   revAiExtractionJobId: extractionJob.id,
    //   status: TranscriptStatus.InProgress,
    // });

    // Create transcriptions
    await createTranscriptionsAndUpdateStatus(data.monologues, resourceInIE, resource);
  } catch (error) {
    log.error(
      "[handleTranscriptSuccess] Error in handleTranscriptSuccess:",
      error
    );
  }
};

const createTranscript = async (
  m: Monologue,
  resourceInInsightEngineId: string
) => {
  const startTime = Math.min(
    ...m.elements.filter((i) => i.ts).map((e) => e.ts as number)
  );
  const endTime = Math.max(...m.elements.map((e) => e.end_ts ?? 0));
  const name = `Speaker ${m.speaker}`;
  let content = "";
  m.elements.forEach((e) => (content += e.value));

  const transcription = await models.Transcription.xCreate({
    resourceInInsightEngineId,
    content,
    startTime,
    nameFromRevAi: name,
    endTime,
  });
  return transcription;
};

const createLiveTranscript = async (data: ResourceLiveTranscript, riieId: string) => {
  return models.Transcription.xCreate({
    resourceInInsightEngineId: riieId,
    startTime: data.startTime,
    endTime: data.endTime,
    content: data.content,
    nameFromRevAi: data.speakerId,
  })
}

export const getReceivers = async (
  meetingUrl: string
): Promise<{ email: string; name: string }[]> => {
  const sessions = await models.Session.xFindBy("meetingUrl", meetingUrl);
  if (sessions.length === 0) {
    return [];
  }
  const receiverUids = sessions
    .filter((s) => s.shouldSendSummaryToEmail)
    .map((s) => ({ uid: s.createdById }));
  const firebaseAuth = await getFirebaseAuth();
  const fUsers = (await firebaseAuth.getUsers(receiverUids)).users;
  if (fUsers.length === 0) {
    return [];
  }
  const receivers = fUsers.map((fUser) => ({
    email: fUser.email,
    name: fUser.displayName,
  }));
  return receivers;
};

export async function createTranscriptionsAndUpdateStatus(monologues: Monologue[] | ResourceLiveTranscript[],
  resourceInIE: { id: string },
  resource: { id: string; createdById: string; sessionId?: string; name: string; createdAt: Date }) {
  log.info("[handleTranscriptSuccess] Creating transcriptions");
  const transcriptions = await Promise.all(
    monologues.map((i) => 'elements' in i ? createTranscript(i, resourceInIE.id) : createLiveTranscript(i, resourceInIE.id))
  );
  log.info("[handleTranscriptSuccess] Created transcriptions", JSON.parse(JSON.stringify(transcriptions, null, 2)));

  const wordsCount = transcriptions.reduce((acc, curr) => {
    const text = (curr?.content ?? "").trim();
    acc += text.split(" ").length;
    return acc;
  }, 0);

  // Update user statistics
  log.info("[handleTranscriptSuccess] Updating user statistics");
  await upsertUserStatistics(resource.createdById, {
    totalTranscriptionsWordCount: wordsCount,
  });

  // Update transcription job status
  log.info("[handleTranscriptSuccess] Updating transcription job status");
  await models.ResourceInInsightEngine.xUpdateById(resourceInIE.id, {
    status: TranscriptStatus.Completed,
  });

  const userDetails = await getFirebaseUserDetails(resource.createdById);
      let timeZone: string = defaultTimezone;
      if (userDetails?.preferences) {
        timeZone = userDetails.preferences.timezone;
      }

  // Update session status if it exists
  if (resource.sessionId) {
    log.debug("[handleTranscriptSuccess] Update session status to completed");
    const sessionId = await models.Session.xUpdateById(resource.sessionId, {
      status: SessionStatus.Completed,
    });

    const session = await models.Session.xFind1ById(resource.sessionId);

    // generate Summary and meta data
    try {
      const { summary, base64Docx, summaryTitle, subjectDate } = await generateSummaryAndStore({
        resourceInInsightEngineId: resourceInIE.id,
        sessionTitle: session.title,
        sessionDate: session.startTime.toISOString(),
        timezone: timeZone
      });

      if (session.shouldSendSummaryToEmail) {
        // create retention setting for the resource
        await models.RetentionSetting.xCreate({
          resourceId: resource.id,
          expiryDate: addDays(new Date(), DATA_RETENTION_DAYS),
        });
        const receivers = await getReceivers(session.meetingUrl);
        await sendSummaryEmail({
          summary,
          base64Docx,
          summaryTitle,
          subjectDate,
          receivers,
          resourceInInsightEngineId: resourceInIE.id,
        });
      }
    } catch (error) {
      log.error(
        "[handleTranscriptSuccess] Error generating summary and sending email:",
        error
      );
    }
  } else if (transcriptions.length > 0) {
    try {
      await generateSummaryAndStore({
        resourceInInsightEngineId: resourceInIE.id,
        sessionTitle: resource.name,
        sessionDate: resource.createdAt.toISOString(),
      });
      log.info(
        "[handleTranscriptSuccess] No session found, summary generated for resource"
      );
    } catch (error) {
      log.error(
        "[handleTranscriptSuccess] Error generating summary for resource:",
        error
      );
    }
  }
}
