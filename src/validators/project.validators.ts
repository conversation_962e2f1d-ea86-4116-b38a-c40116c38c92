// project invite user
import { InvitationStatus } from "@/schemas/project/ProjectInvitation.model";
import { ProjectMemberRole } from "@/schemas/project/ProjectMember.model";
import { ProjectAccessRequestStatus } from "@/schemas/project/ProjectAccessRequest.model";
import { z } from "zod";


export const projectIdParamsValidator = z.object({
    projectId: z.string(),
});

/**
 * Project code params validator
 */
export const projectCodeParamsValidator = z.object({
    code: z.string(),
});

export const inviteUserToProjectBodyValidator = z.object({
    email: z.string().email(),
    role: z.nativeEnum(ProjectMemberRole),
});

export const inviteUserToProjectPayloadValidator = projectIdParamsValidator
    .merge(inviteUserToProjectBodyValidator);

export const acceptInvitationToProjectValidator = projectCodeParamsValidator

export const rejectInvitationToProjectValidator = projectCodeParamsValidator

export type ProjectIdParamsDTO = z.infer<typeof projectIdParamsValidator>;


export type InviteUserToProjectDTO = z.infer<typeof inviteUserToProjectBodyValidator>;
export type InviteUserToProjectPayloadDTO = z.infer<typeof inviteUserToProjectPayloadValidator>;
export type AcceptInvitationToProjectDTO = z.infer<typeof acceptInvitationToProjectValidator>;
export type RejectInvitationToProjectDTO = z.infer<typeof rejectInvitationToProjectValidator>;

/**
 * Create project shared link
 */
export const createProjectSharedLinkValidator = z.object({
    expriedInDays: z.number().optional(),
    maxAccessCount: z.number().optional(),
});

export const verifyProjectSharedLinkValidator = z.object({
    token: z.string(),
});

export type CreateProjectSharedLinkDTO = z.infer<typeof createProjectSharedLinkValidator> & ProjectIdParamsDTO;
export type VerifyProjectSharedLinkDTO = z.infer<typeof verifyProjectSharedLinkValidator>;

export const accessRequestIdParamsValidator = z.object({
    accessRequestId: z.string(),
}).merge(projectIdParamsValidator);

export const responseProjectAccessRequestValidator = z.object({
    isApproved: z.boolean(),
});

export type AccessRequestIdParamsDTO = z.infer<typeof accessRequestIdParamsValidator>;
export type ApproveProjectAccessRequestDTO = z.infer<typeof responseProjectAccessRequestValidator>;


/**
 * Change project member role
 */
export const changeProjectMemberRoleValidator = z.object({
    role: z.nativeEnum(ProjectMemberRole),
});

export const projectMemberIdParamsValidator = z.object({
    memberUserId: z.string(),
}).merge(projectIdParamsValidator);

export type UpdateProjectMemberParamsDTO = z.infer<typeof projectMemberIdParamsValidator>;
export type UpdateProjectMemberDTO = z.infer<typeof changeProjectMemberRoleValidator>;

/**
 * Project invitation status query validator
 */
export const projectInvitationStatusQueryValidator = z.object({
    status: z.nativeEnum(InvitationStatus).optional(),
});

export type ProjectInvitationStatusQueryDTO = z.infer<typeof projectInvitationStatusQueryValidator>;

/**
 * Project access request status query validator
 */
export const projectAccessRequestStatusQueryValidator = z.object({
    status: z.nativeEnum(ProjectAccessRequestStatus).optional(),
});

export const resendInvitationValidator = z.object({
    email: z.string().email()
});

/**
 * Cancel invitation params validator
 */
export const cancelInvitationParamsValidator = z.object({
    projectId: z.string(),
    inviteId: z.string(),
});

export type CancelInvitationParamsDTO = z.infer<typeof cancelInvitationParamsValidator>;

/**
 * Create project resource validator
 */
export const createProjectResourceValidator = z.object({
    uploadedFileName: z.string(),
    fileName: z.string(),
    fileSize: z.number(),
    fileLastModified: z.string(),
    transcript: z.array(z.object({
        sentence: z.string(),
        startTime: z.number(),
        endTime: z.number(),
        speakerId: z.string(),
        content: z.string(),
    })),
});

type LiveTranscript = {
    sentence: string;
    startTime: number;
    endTime: number;
    speakerId: string;
    content: string;
}