import * as reqValidators from "@/validators/project.validators";
import { Router } from "express";
import checkAuthMiddleware from "@/middlewares/checkAuth";
import {
  isProjectOwnerOrInvitedUserGuard,
  projectOwnerGuard,
} from "@/middlewares/project";
import {
  requestBodyValidator,
  requestParamsValidator,
  requestQueryValidator,
} from "@/middlewares/request.validators";

import * as projectController from "@/controllers/project.controller";
import createProjectFolderHandler from "@/handlers/projects/createProjectFolderHandler";
import createProjectHandler from "@/handlers/projects/createProjectHandler";
import deleteProjectFolderHandler from "@/handlers/projects/deleteProjectFolderHandler";
import deleteProjectHandler from "@/handlers/projects/deleteProjectHandler";
import getProjectFolderDetailsHandler from "@/handlers/projects/getProjectFolderDetailsHandler";

export const projectRouter = Router();

projectRouter.use(checkAuthMiddleware);

const isProjectOwnerValidator = projectOwnerGuard();
const isProjectOwnerOrInvitedUserValidator = isProjectOwnerOrInvitedUserGuard();

/**
 * Only project owner can invite a user to a project via email
 */
projectRouter.post(
  "/invite/:projectId",
  isProjectOwnerValidator,
  requestBodyValidator(reqValidators.inviteUserToProjectBodyValidator),
  requestParamsValidator(reqValidators.projectIdParamsValidator),
  projectController.inviteUserToProjectViaEmail
);

/**
 * Invited user can accept an invitation to a project
 */
projectRouter.post(
  "/invitation/accept/:projectId",
  requestBodyValidator(reqValidators.acceptInvitationToProjectValidator),
  requestParamsValidator(reqValidators.projectIdParamsValidator),
  projectController.acceptInvitationToProject
);

/**
 * Only invited user can reject an invitation to a project
 */
projectRouter.post(
  "/invitation/reject/:projectId",
  requestBodyValidator(reqValidators.rejectInvitationToProjectValidator),
  requestParamsValidator(reqValidators.projectIdParamsValidator),
  projectController.rejectInvitationToProject
);

/**
 * Resend expired invitation
 */
projectRouter.post(
  "/invitation/resend/:projectId",
  isProjectOwnerValidator,
  requestParamsValidator(reqValidators.projectIdParamsValidator),
  requestBodyValidator(reqValidators.resendInvitationValidator),
  projectController.resendInvitation
);

/**
 * Only project owner can cancel a pending invitation
 */
projectRouter.delete(
  "/invitation/:projectId/:inviteId",
  isProjectOwnerValidator,
  requestParamsValidator(reqValidators.cancelInvitationParamsValidator),
  projectController.cancelProjectInvitation
);

/**
 * Only project owner can get all invitations of a project
 */
projectRouter.get(
  "/invitations/:projectId",
  isProjectOwnerValidator,
  requestParamsValidator(reqValidators.projectIdParamsValidator),
  requestQueryValidator(reqValidators.projectInvitationStatusQueryValidator),
  projectController.getProjectInvitations
);

/**
 * Only project owner can get all members of a project
 */
projectRouter.get(
  "/members/:projectId",
  isProjectOwnerValidator,
  requestParamsValidator(reqValidators.projectIdParamsValidator),
  projectController.getProjectMembers
);

projectRouter.get(
  "/member/me/:projectId",
  requestParamsValidator(reqValidators.projectIdParamsValidator),
  projectController.getMyProjectMembership
);

/**
 * Only project owner can get all members of a project
 */
projectRouter.put(
  "/member/change-role/:projectId/:memberUserId",
  isProjectOwnerValidator,
  requestParamsValidator(reqValidators.projectMemberIdParamsValidator),
  requestBodyValidator(reqValidators.changeProjectMemberRoleValidator),
  projectController.changeProjectMemberRole
);

/**
 * Only project owner can create a shared link
 */
projectRouter.post(
  "/shared-link/:projectId",
  isProjectOwnerValidator,
  requestParamsValidator(reqValidators.projectIdParamsValidator),
  requestBodyValidator(reqValidators.createProjectSharedLinkValidator),
  projectController.createProjectSharedLink
);

/**
 * Invited user can verify the shared link
 */
projectRouter.post(
  "/shared-link/verify/:projectId",
  requestBodyValidator(reqValidators.verifyProjectSharedLinkValidator),
  projectController.verifyProjectSharedLink
);

/**
 * Only project owner can get all shared links of a project
 */
projectRouter.get(
  "/shared-link/:projectId",
  isProjectOwnerValidator,
  requestParamsValidator(reqValidators.projectIdParamsValidator),
  projectController.getProjectSharedLinks
);

/**
 * Only project owner can accept/reject a project access request
 */
projectRouter.put(
  "/access-request/response/:projectId/:accessRequestId",
  isProjectOwnerValidator,
  requestParamsValidator(reqValidators.accessRequestIdParamsValidator),
  requestBodyValidator(reqValidators.responseProjectAccessRequestValidator),
  projectController.approveOrRejectAccessRequest
);

projectRouter.get(
  "/access-request/:projectId",
  isProjectOwnerValidator,
  requestParamsValidator(reqValidators.projectIdParamsValidator),
  requestQueryValidator(reqValidators.projectAccessRequestStatusQueryValidator),
  projectController.getProjectAccessRequests
);

projectRouter.post("/", checkAuthMiddleware, createProjectHandler);

projectRouter.post(
  "/project-default",
  checkAuthMiddleware,
  projectController.createProjectDefault
);

projectRouter.get(
  "/:projectId",
  checkAuthMiddleware,
  requestParamsValidator(reqValidators.projectIdParamsValidator),
  isProjectOwnerOrInvitedUserValidator,
  projectController.getProjectDetails
);

projectRouter.put(
  "/:projectId",
  checkAuthMiddleware,
  requestParamsValidator(reqValidators.projectIdParamsValidator),
  isProjectOwnerOrInvitedUserValidator,
  projectController.updateProject
);

projectRouter.delete("/:id", checkAuthMiddleware, deleteProjectHandler);

projectRouter.post(
  "/:id/folder",
  checkAuthMiddleware,
  createProjectFolderHandler
);

projectRouter.get(
  "/:id/folder/:folderId",
  checkAuthMiddleware,
  getProjectFolderDetailsHandler
);

projectRouter.delete(
  "/:id/folder/:folderId",
  checkAuthMiddleware,
  deleteProjectFolderHandler
);

projectRouter.delete(
  "/member/:projectId/:memberUserId",
  checkAuthMiddleware,
  isProjectOwnerValidator,
  requestParamsValidator(reqValidators.projectMemberIdParamsValidator),
  projectController.deleteProjectMember
);

/**
 * Authenticated user can leave a project they are a member of
 */
projectRouter.delete(
  "/:projectId/members/leave",
  requestParamsValidator(reqValidators.projectIdParamsValidator),
  projectController.leaveProject
);


/**
 * Authenticated user can create a project resource and transcript
 * @param {string} projectId - The ID of the project
 * @payload {string} transcript - The transcript of the file
 */
projectRouter.post(
  "/:projectId/resource",
  checkAuthMiddleware,
  requestParamsValidator(reqValidators.projectIdParamsValidator),
  requestBodyValidator(reqValidators.createProjectResourceValidator),
  projectController.createProjectResource
);
