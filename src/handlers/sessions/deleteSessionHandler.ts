import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import { SessionStatus } from "@/schemas/session/Session.model";
import { recallAiClient } from "@/services/recall";
import { sendMeetingDeletedEmail } from "@/services/email";

const deleteSessionHandler = async (
  req: express.Request,
  res: express.Response,
) => {
  try {
    const { id: sessionId } = req.params;
    const currentUser = res.locals.user;

    const session = await models.Session.xFind1({
      createdById: currentUser.id,
      id: sessionId,
    });

    if (!session) {
      res.status(404).json({ message: "Session not found" });
      return;
    }

    // If session is in progress, remove bot from call
    if (session.status === SessionStatus.InProgress && session.recallBotId) {
      await recallAiClient.removeBotFromCall(session.recallBotId);
    }

    if(session.shouldSendSummaryToEmail && currentUser.id === session.createdById) {
      // Remove existing summary
      const resource = await models.Resource.xFind1({
        sessionId: session.id,
      });
      if(resource) {
        const riie = await models.ResourceInInsightEngine.xFind1({
          resourceId: resource.id,
        });
  
        // Remove transcription if exists
        const transcript = await models.Transcription.xFind1({
          resourceInInsightEngineId: riie.id,
        });
        if (transcript) {
          await models.Transcription.xUpdateById(transcript.id, { isDeleted: true, deletedAt: new Date() });
        }

        // Remove notes in insight engine
        const notes = await models.Note.xFind({
          resourceId: resource.id,
        });
        for (const note of notes) {
          await models.Note.xUpdateById(note.id, { isDeleted: true, deletedAt: new Date() });
        }
  
        // Send email to
        await sendMeetingDeletedEmail({
          to: currentUser.email,
          subject: `${session.title} has been deleted`,
          substitutions: {
            meetingTitle: session.title,
            username: currentUser.name || 'there',
          }
        });
      }
    }

    await models.Session.xDestroyById(sessionId);

    res.status(200).json({ message: "Success" });
  } catch (error) {
    log.error(`Failed to delete session: ${error.message}`);
    throw new IntentionalError("Failed to delete session");
  }
};

export default deleteSessionHandler;
