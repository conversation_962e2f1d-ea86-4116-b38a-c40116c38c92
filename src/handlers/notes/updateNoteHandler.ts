import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import { Note } from "@/schemas/note/Note.model";
import { Project } from "@/schemas/project/Project.model";
import {
  ProjectMember,
  ProjectMemberRole,
} from "@/schemas/project/ProjectMember.model";

const updateNoteHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const { id } = req.params;
    const { content, title } = req.body;
    const currentUser = res.locals.user;

    if (!currentUser) {
      res.status(401).json({ error: "Unauthorized" });
      return;
    }

    // First, get the note to check if it belongs to a project
    const noteQuery = await models.Note.findOne({
      where: {
        id,
      },
      include: [
        {
          model: models.Project,
          as: "project",
          required: false,
        },
      ],
    });

    const note = noteQuery?.toJSON() as Note & { project: Project };
    if (!note) {
      res.status(404).json({ error: "Note not found" });
      return;
    }

    // If note belongs to a project, validate user permissions
    if (note.project?.id) {
      const projectQuery = await models.Project.findOne({
        where: {
          id: note.project.id,
        },
        include: [
          {
            model: models.ProjectMember,
            where: {
              userId: currentUser.id,
            },
            as: "members",
            required: false,
          },
        ],
      });

      const project = projectQuery?.toJSON() as Project & {
        members: ProjectMember[];
      };

      if (!project) {
        res.status(404).json({
          message:
            "Project not found or you don't have permission to access it",
        });
        return;
      }

      const projectMember = project.members?.[0];

      if (!projectMember && project.createdById !== currentUser.id) {
        res.status(403).json({
          message: "You are not in this project member list nor project owner",
        });
        return;
      }

      if (projectMember && projectMember?.role !== ProjectMemberRole.EDITOR && projectMember?.role !== ProjectMemberRole.COMMENTER) {
        res
          .status(403)
          .json({ message: "You don't have permission to update notes in this project" });
        return;
      }
    }

    const updatedNote = await models.Note.update(
      {
        content,
        title,
        updatedById: currentUser.id,
      },
      {
        where: { id },
      }
    );

    if (!updatedNote) {
      res.status(404).json({ error: "Note not found" });
      return;
    }

    res.status(200).json(updatedNote);
  } catch (error) {
    log.error("[updateNoteHandler] Failed to update note", error);
    throw new IntentionalError("Failed to update note");
  }
};

export default updateNoteHandler;
