import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import { Project } from "@/schemas/project/Project.model";
import {
  ProjectMember,
  ProjectMemberRole,
} from "@/schemas/project/ProjectMember.model";

const createNoteHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const { content, resourceId = null, title, projectId = null } = req.body;
    const currentUser = res.locals.user;

    if (content == undefined || !title || (!resourceId && !projectId)) {
      res.status(400).json({ message: "Missing required fields" });
      return;
    }

    // If note belongs to a project, validate user permissions
    if (projectId) {
      const projectQuery = await models.Project.findOne({
        where: {
          id: projectId,
        },
        include: [
          {
            model: models.ProjectMember,
            where: {
              userId: currentUser.id,
            },
            as: "members",
            required: false,
          },
        ],
      });

      const project = projectQuery?.toJSON() as Project & {
        members: ProjectMember[];
      };

      if (!project) {
        res.status(404).json({
          message:
            "Project not found or you don't have permission to access it",
        });
        return;
      }

      const projectMember = project.members?.[0];

      if (!projectMember && project.createdById !== currentUser.id) {
        res.status(403).json({
          message: "You are not in this project member list nor project owner",
        });
        return;
      }

      if (projectMember && projectMember?.role !== ProjectMemberRole.EDITOR && projectMember?.role !== ProjectMemberRole.COMMENTER) {
        res
          .status(403)
          .json({ message: "You don't have permission to create notes in this project" });
        return;
      }
    }

    const note = await models.Note.create({
      content,
      resourceId,
      projectId,
      title,
      createdById: currentUser.id,
      updatedById: currentUser.id,
    });

    res.status(200).json(note);
  } catch (error) {
    log.error(`[createNote] Failed to create note`, error);
    throw new IntentionalError("Failed to create note");
  }
};

export default createNoteHandler;
