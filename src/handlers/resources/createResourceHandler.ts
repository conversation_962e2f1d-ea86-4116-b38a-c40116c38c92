import express from "express";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";
import { gcsResourceFolder } from "@/config";
import { IEUploadAction } from "@/schemas/insightEngine/InsightEngine.model";
import { createNewResource } from "@/schemas/resource/utils";
import { createTranscodingJob } from "@/services/resourceTranscodedJob/resourceTranscodedJob.service";
import { FILE_EXTENSIONS, TRANSCODED_FILE_PREFIX } from "@/constants/storage";
import { TranscodingMode } from "@/schemas/transcoded/ResourceTranscodedJob.model";
import { isDocumentOrImage, isVideoOrAudio } from "@/utils/file";

/**
 * Interface for resource creation data
 */
interface ResourceData {
  gcsFilePath: string;
  fileLastModified?: Date;
  fileName: string;
  fileSize?: number;
  userId: string;
  title: string;
  uploadAction: IEUploadAction;
  projectId?: string;
  folderId?: string;
  transcodedUrl?: string;
  isTranscoding?: boolean;
}

/**
 * <PERSON><PERSON> for creating a new resource, with optional transcoding
 */
const createResourceHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const {
      fileName,
      fileSize,
      fileLastModified,
      uploadedFileName,
      projectId,
      folderId,
      transcoded,
      mode,
    } = req.body;
    const currentUser = res.locals.user;

    if (!fileName) {
      res.status(400).json({ message: "Missing file name" });
      return;
    }

    if (!uploadedFileName) {
      res.status(400).json({ message: "Missing uploaded file name" });
      return;
    }

    const filePath = `${gcsResourceFolder}/${uploadedFileName}`;
    let resourceData: ResourceData = {
      gcsFilePath: filePath,
      fileLastModified,
      fileName,
      fileSize,
      userId: currentUser.id,
      title: fileName,
      uploadAction: IEUploadAction.UPLOAD,
      projectId,
      folderId,
    };

    // Handle transcoding only for video/audio files
    // Skip transcoding for documents and images
    let transcodedFileName = "";
    const isDocOrImage = isDocumentOrImage(fileName);
    const isVideo = isVideoOrAudio(fileName);

    if (transcoded && isVideo && !isDocOrImage) {
      // Replace file extension with .mp4 - supporting multiple formats
      const baseFileName = uploadedFileName.replace(
        FILE_EXTENSIONS.MKV,
        FILE_EXTENSIONS.MP4
      );

      transcodedFileName = `${TRANSCODED_FILE_PREFIX}${baseFileName}`;

      // Add transcoding info to resource
      resourceData.transcodedUrl = `${gcsResourceFolder}/${transcodedFileName}`;
      resourceData.isTranscoding = true;
    }

    const { resource } = await createNewResource(resourceData);

    // Only attempt transcoding for video/audio files that aren't documents or images
    if (transcoded && isVideo && !isDocOrImage) {
      try {
        // Validate that the job mode is valid
        const transcodingMode = mode as TranscodingMode;

        // Use the new service to create the transcoding job
        const result = await createTranscodingJob(
          filePath,
          transcodedFileName,
          resource.id,
          transcodingMode
        );

        if (!result.success) {
          log.error(
            `[createResourceHandler] Failed to create transcoding job: ${result.message}`
          );
        } else {
          log.info(
            `Created transcoding job ${result.jobId || ""} for resource ${
              resource.id
            }`
          );
        }
      } catch (error) {
        log.error(
          `[createResourceHandler] Failed to create transcoding job`,
          error
        );
        // Continue without failing the resource creation
      }
    } else if (isDocOrImage) {
      log.info(
        `[createResourceHandler] Skipping transcoding for document/image file: ${fileName}`
      );
    }

    res.status(200).json(resource);
  } catch (error) {
    log.error(`[createResourceHandler] Failed to create resource`, error);
    throw new IntentionalError("Failed to create resource");
  }
};

export default createResourceHandler;
