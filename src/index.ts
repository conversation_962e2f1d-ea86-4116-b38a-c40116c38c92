import cors from "cors";
import express from "express";
import "express-async-errors";

import apiRoutes from "@/api";
import { db } from "@/schemas";
import { log } from "@/services/logger";
import posthog from "@/services/posthog";
import expressErrorHandler from "./handlers/expressErrorHandler";
import { initializeFirebase } from "./services/firebase";
import { initPubSub } from "./services/pubsub";
import { SchedulerService } from "./services/scheduler";

const app = express();

const PORT = parseInt(process.env.PORT || "8080", 10);

app.use(cors());
app.use(express.json({ limit: "50mb" }));
app.use(express.urlencoded({ limit: "50mb", extended: true }));
app.use("/api", apiRoutes);

app.use(expressErrorHandler);

const main = async () => {
  console.info("🚀 Starting Aida Service...");
  console.info(`Environment: ${process.env.NODE_ENV || "development"}`);
  console.info(`Build Mode: ${process.env.BUILD_MODE || "not set"}`);
  console.info(`Port: ${PORT}`);
  console.info(`GCP Project ID: ${process.env.GCP_PROJECT_ID || "not set"}`);
  console.info(`Working Directory: ${process.cwd()}`);

  try {
    console.info("🔧 Initializing services...");

    // Initialize Firebase with timeout
    console.info("🔥 Initializing Firebase...");
    const firebasePromise = Promise.race([
      initializeFirebase(),
      new Promise((_, reject) =>
        setTimeout(
          () => reject(new Error("Firebase initialization timeout")),
          30000
        )
      ),
    ]);

    // Initialize Database with timeout
    console.info("🗄️ Authenticating database...");
    const dbPromise = Promise.race([
      db.authenticate(),
      new Promise((_, reject) =>
        setTimeout(
          () => reject(new Error("Database authentication timeout")),
          30000
        )
      ),
    ]);

    await Promise.all([firebasePromise, dbPromise]);
    console.info("✅ Services initialized successfully");

    // Sync DB
    console.info("🗄️ Syncing database...");
    // TODO: Remove "alter" option later; keep it here for now for faster development due to many changes with schema
    await db.sync({
      alter: true,
    });
    console.info("✅ Database synced successfully");

    // Subscribe to PubSub messages
    console.info("📡 Initializing PubSub...");
    await initPubSub();
    console.info("✅ PubSub initialized successfully");
  } catch (error) {
    console.error("❌ Failed to initialize services:", error);
    throw error;
  }

  // Initialize scheduler
  // const schedulerService = new SchedulerService();
  // schedulerService.start();

  // Start server - bind to 0.0.0.0 for Cloud Run
  const server = app.listen(PORT, "0.0.0.0", () => {
    console.info(`Successfully started the server on PORT: ${PORT}`);
    console.info(`Server listening on 0.0.0.0:${PORT}`);
    console.info(`Environment: ${process.env.NODE_ENV || "development"}`);
    console.info(`Build Mode: ${process.env.BUILD_MODE || "not set"}`);
    // Capture server start event
    posthog.capture({
      distinctId: "server",
      event: "server_started",
      properties: {
        port: PORT,
        environment: process.env.NODE_ENV || "development",
      },
    });
  });

  // Graceful shutdown handler for Cloud Run
  const gracefulShutdown = async (signal: string) => {
    console.info(`Received ${signal}. Starting graceful shutdown...`);

    // Stop accepting new requests
    server.close(async () => {
      console.info("HTTP server closed");

      try {
        // Close database connections
        await db.close();
        console.info("Database connections closed");

        // Capture shutdown event
        posthog.capture({
          distinctId: "server",
          event: "server_shutdown",
          properties: {
            signal,
            environment: process.env.NODE_ENV || "development",
          },
        });

        console.info("Graceful shutdown completed");
        process.exit(0);
      } catch (error) {
        console.error("Error during graceful shutdown:", error);
        process.exit(1);
      }
    });

    // Force shutdown after 30 seconds
    setTimeout(() => {
      console.error("Forced shutdown after timeout");
      process.exit(1);
    }, 30000);
  };

  // Handle shutdown signals
  process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
  process.on("SIGINT", () => gracefulShutdown("SIGINT"));
};

// Add startup timeout to prevent hanging
const startupTimeout = setTimeout(() => {
  console.error("💥 FATAL: Application startup timeout after 2 minutes");
  console.error(
    "This usually indicates a database connection or service initialization issue"
  );
  process.exit(1);
}, 120000); // 2 minutes

main()
  .then(() => {
    clearTimeout(startupTimeout);
    console.info("🎉 Application startup completed successfully");
  })
  .catch((err) => {
    clearTimeout(startupTimeout);
    console.error("💥 FATAL: Application startup failed:", err);
    log.stack(err, "fatal");
    // Capture fatal error event
    posthog.capture({
      distinctId: "server",
      event: "fatal_error",
      properties: {
        error: err.message,
        stack: err.stack,
      },
    });
    process.exit(1);
  });
