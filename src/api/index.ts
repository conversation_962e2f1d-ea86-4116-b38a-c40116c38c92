import express from "express";
import checkAuthMiddleware from "../middlewares/checkAuth";
import revAiCallbackHandler from "@/handlers/callbacks/revAiCallbackHandler";
import getUserStatisticsHandler from "@/handlers/users/getUserStatisticsHandler";
import recordMeetingHandler from "@/handlers/sessions/recordMeetingHandler";
import recallCallbackHandler from "@/handlers/callbacks/recallCallbackHandler";
import getSessionsHandler from "@/handlers/sessions/getSessionsHandler";
import deleteSessionHandler from "@/handlers/sessions/deleteSessionHandler";
import getSessionDetailsHandler from "@/handlers/sessions/getSessionDetailsHandler";
import retrySessionHandler from "@/handlers/sessions/retrySessionHandler";
import getOngoingSessionsHandler from "@/handlers/sessions/getOngoingSessionsHandler";
import mediaSyncJobHandler from "@/handlers/resources/mediaSyncJobHandler";
import internalVerifyMiddleware from "@/middlewares/internalVerify";
import getUserInitialContextHandler from "@/handlers/users/getUserInitialContextHandler";
import createNoteHandler from "@/handlers/notes/createNoteHandler";
import getNotesHandler from "@/handlers/notes/getNotesHandler";
import deleteNoteHandler from "@/handlers/notes/deleteNoteHandler";
import updateNoteHandler from "@/handlers/notes/updateNoteHandler";
import { userAidaHandler } from "@/handlers/user-aida/handler";
import { projectRouter } from "@/routes/project.routes";
import { getUserAccessibleProjectIdsContext } from "@/middlewares/project";
import { resourcesRouter } from "@/routes/resources.routes";
import { liveTranscribeRouter } from "@/routes/live-transcribe.routes";
import { syncUsersHandler } from "@/services/firebase";
import { getProjects } from "@/controllers/project.controller";
import createUserFeedbackHandler from "@/handlers/userFeedback/createUserFeedbackHandler";
import getUserFeedbackHandler from "@/handlers/userFeedback/getUserFeedbackHandler";
import createUserEmailFeedbackHandler from "@/handlers/userFeedback/createUserEmailFeedbackHandler";

const router = express.Router();

// Notes
router.post("/note", checkAuthMiddleware, createNoteHandler);
router.get(
  "/notes",
  checkAuthMiddleware,
  getUserAccessibleProjectIdsContext,
  getNotesHandler
);
router.put(
  "/note/:id",
  checkAuthMiddleware,
  getUserAccessibleProjectIdsContext,
  updateNoteHandler
);
router.delete(
  "/note/:id",
  checkAuthMiddleware,
  getUserAccessibleProjectIdsContext,
  deleteNoteHandler
);

// Pubsub
router.post(
  "/internal-cloudlab/media-sync-job",
  internalVerifyMiddleware,
  mediaSyncJobHandler
);

// Sessions
router.post(
  "/session/record-meeting",
  checkAuthMiddleware,
  recordMeetingHandler
);
router.get("/sessions", checkAuthMiddleware, getSessionsHandler);
router.get("/sessions/ongoing", checkAuthMiddleware, getOngoingSessionsHandler);
router.get("/session/:id", checkAuthMiddleware, getSessionDetailsHandler);
router.delete("/session/:id", checkAuthMiddleware, deleteSessionHandler);
router.post("/session/retry/:id", checkAuthMiddleware, retrySessionHandler);

// Live Transcription - Use dedicated router
router.use("/live-transcribe", liveTranscribeRouter);

// User
router.get(
  "/user/initial-context",
  checkAuthMiddleware,
  getUserAccessibleProjectIdsContext,
  getUserInitialContextHandler
);

router.get("/user/statistics", checkAuthMiddleware, getUserStatisticsHandler);
router.get(
  "/projects",
  checkAuthMiddleware,
  getUserAccessibleProjectIdsContext,
  getProjects
);

router.use("/project", projectRouter);
router.use(resourcesRouter);

// Project

//#region User Feedback
router.post("/user-feedback", createUserFeedbackHandler);
router.get(
  "/user-feedback/:resourceId",
  checkAuthMiddleware,
  getUserFeedbackHandler
);
router.post("/user-feedback/email", createUserEmailFeedbackHandler);

//#endregion

// Callbacks
router.post("/callback/revAi", revAiCallbackHandler);
router.post("/callback/recallAi", recallCallbackHandler);

// Anonymous Invite Aida Confirmation
router.get("/confirm/aida", userAidaHandler);

router.get("/_internal_user-sync", syncUsersHandler);

// Health check endpoint for Cloud Run
router.get("/health", (req, res) => {
  res.status(200).json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || "development",
  });
});

export default router;
