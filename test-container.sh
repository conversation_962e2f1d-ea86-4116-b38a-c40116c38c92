#!/bin/bash

# Test container locally to debug issues
set -e

echo "🧪 Testing container locally..."

# Build the container
echo "🔨 Building container..."
docker build -t aida-service-test .

# Test environment variables
echo "🔧 Testing with environment variables..."
docker run --rm \
  -e BUILD_MODE=development \
  -e GCP_PROJECT_ID=aida-22a9a \
  -e PORT=8080 \
  -p 8080:8080 \
  aida-service-test

echo "✅ Container test completed"
